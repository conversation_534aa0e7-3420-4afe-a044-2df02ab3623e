#ifndef __ENGINE_H
#define __ENGINE_H

#include <Windows.h>

namespace Engine
{
	namespace KWindowCollector
	{
		static void(__cdecl* ProcessDestroy)() = (void(__cdecl*)())0x006DD810;
	}

	namespace KGameSys
	{
		static void(__cdecl* ChkDataMode)(int a1, const char* Format) = (void(__cdecl*)(int, const char*))0x007402D0;
	}

	namespace CD3DApplication
	{
		//static HRESULT(__thiscall* Render3DEnvironment)(void* _this) = (HRESULT(__thiscall*)(void*))0x00482D90;
		static int(__fastcall* Render3DEnvironment)(void* _this) = (int(__fastcall*)(void*))0x00482D90;
		static int(__thiscall* Reset3DEnvironment)(void* _this) = (int(__thiscall*)(void*))0x004821B0;
		static float(__stdcall* DXUtil_Timer)(int a1) = (float(__stdcall*)(int))0x00487F00;
	}



}

#endif