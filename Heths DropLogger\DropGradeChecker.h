
bool checkUpgrades(std::string msg) {
	if (containsStr(msg, "Dragon Spirit Scroll") ||
		containsStr(msg, "Stone of ") ||
		containsStr(msg, "Bead of Fire") 
		) {
		return true;
	}
	return false;
}

bool checkG50AW(std::string msg) {
	if ((containsStr(msg, "Diamond") && containsStr(msg, "Armor")) ||
		(containsStr(msg, "Diamond") && containsStr(msg, "Helmet")) ||
		(containsStr(msg, "Diamond") && containsStr(msg, "Gloves")) ||
		(containsStr(msg, "Diamond") && containsStr(msg, "Shoes")) ||
		(containsStr(msg, "Diamond") && containsStr(msg, "Shorts")) ||
		(containsStr(msg, "Diamond") && containsStr(msg, "suit")) ||
		(containsStr(msg, "Diamond") && containsStr(msg, "Hood")) ||
		(containsStr(msg, "Diamond") && containsStr(msg, "Boots")) ||
		containsStr(msg, "White DaeChangEau") ||
		containsStr(msg, "Golden Treasure Sword") || containsStr(msg, "Butterfly-Shaped Treasure Sword") || containsStr(msg, "Black Golden Treasure Sword") ||
		containsStr(msg, "Giant Sword") || containsStr(msg, "Revengeful Sword") || containsStr(msg, "Butterfly-Shaped Giant Sword") ||
		containsStr(msg, "Steel Bow with Crow's Feather") || containsStr(msg, "Steel Bow with Golden Crow's Feather") || containsStr(msg, "Stick with Black Crow's Feather") ||
		containsStr(msg, "Stick with Blue Crow's Feather") || containsStr(msg, "Golden Treasure dagger") || containsStr(msg, "Butterfly-Shaped dagger") || containsStr(msg, "Revengeful dagger")
		) {
		return true;
	}
	return false;
}


bool checkG53W(std::string msg) {
	if ((containsStr(msg, "Diamond") && containsStr(msg, "Sword")) ||
		(containsStr(msg, "Diamond") && containsStr(msg, "Bow")) ||
		(containsStr(msg, "Diamond") && containsStr(msg, "Stick")) ||
		(containsStr(msg, "Diamond") && containsStr(msg, "dagger"))
		) {
		return true;
	}
	return false;
}

bool checkG56W(std::string msg) {
	if (containsStr(msg, "Ancient Treasure Sword") || containsStr(msg, "General's Treasure Sword") || containsStr(msg, "Ancient Dynasty's Treasure Sword") ||
		containsStr(msg, "Ancient Giant Sword") || containsStr(msg, "General's Giant Sword") || containsStr(msg, "Ancient Dynasty's Giant Sword") ||
			containsStr(msg, "Ancient Tribal Mac's Bow") || containsStr(msg, "General of Ancient Tribal Mac's Bow") || containsStr(msg, "Ancient Dynasty of Tribal Mac's Bow") ||
			containsStr(msg, "Ancient Stick") || containsStr(msg, "General's Stick") || containsStr(msg, "Ancient Dynasty's Stick") || 
			containsStr(msg, "Ancient dagger") || containsStr(msg, "General's dagger") || containsStr(msg, "Ancient Dynasty's dagger") 
		) {
		return true;
	}
	return false;
}



bool checkG55AW(std::string msg) {
	if (containsStr(msg, "Armor of Doggebi") ||
		containsStr(msg, "Helmet of Doggebi") ||
		containsStr(msg, "Gloves of Doggebi") ||
		containsStr(msg, "Shorts of Doggebi") ||
		containsStr(msg, "Shoes of Doggebi") ||
		containsStr(msg, "DaeChangEau of Doggebi") ||
		containsStr(msg, "Decoration of Doggebi") ||
		(containsStr(msg, "Sword") && containsStr(msg, "Doggebi")) ||
		(containsStr(msg, "Bow") && containsStr(msg, "Doggebi")) ||
		(containsStr(msg, "Stick") && containsStr(msg, "Doggebi")) || 
		(containsStr(msg, "dagger") && containsStr(msg, "Doggebi")) ||
		(containsStr(msg, "Doggebi") && containsStr(msg, "of thief"))
		) {
		return true;
	}
	return false;
}




bool checkG60AW(std::string msg) {
	if (containsStr(msg, "Golden Scaled") ||
		containsStr(msg, "Golden Armor") ||
		containsStr(msg, "Golden DaeChangEau") ||
		containsStr(msg, "Golden Sword") ||
		containsStr(msg, "Golden Giant Sword") ||
		containsStr(msg, "Golden Bow") ||
		containsStr(msg, "Golden Stick") ||
		containsStr(msg, "Golden dagger") ||
		(containsStr(msg, "Golden") && containsStr(msg, "of thief"))
		) {
		return true;
	}
	return false;
}

bool checkG65AW(std::string msg) {
	if ((containsStr(msg, "Blood Dragon") && containsStr(msg, "Sword")) ||
		(containsStr(msg, "Blood Dragon") && containsStr(msg, "Bow")) ||
		(containsStr(msg, "Blood Dragon") && containsStr(msg, "Stick")) ||
		(containsStr(msg, "Blood Dragon") && containsStr(msg, "Dagger")) ||
		(containsStr(msg, "Blood Dragon") && containsStr(msg, "Scaled")) ||
		(containsStr(msg, "Blood Dragon") && containsStr(msg, "Armor")) ||
		(containsStr(msg, "Blood Dragon") && containsStr(msg, "of Thief")) ||
		(containsStr(msg, "Blood Dragon") && containsStr(msg, "DaeChangEau")) ||

		(containsStr(msg, "Blood Dragon") && containsStr(msg, "Dragon Sword of Sky")) ||
		(containsStr(msg, "Blood Dragon") && containsStr(msg, "Dragon Giant Sword of Sky"))||
		(containsStr(msg, "Blood Dragon") && containsStr(msg, "Dragon Bow of Sky"))||
		(containsStr(msg, "Blood Dragon") && containsStr(msg, "Dragon Stick of Sky"))||
		(containsStr(msg, "Blood Dragon") && containsStr(msg, "Dragon dagger of Sky")) ||
		(containsStr(msg, "Blood Dragon") && containsStr(msg, "Dragon Sword of Sky"))
	){
		return true;
	}
	return false;
}


bool checkG70AW(std::string msg) {
	if ((containsStr(msg, "Heavenly Executioner") && containsStr(msg, "Sword")) ||
		(containsStr(msg, "Heavenly Executioner") && containsStr(msg, "Bow")) ||
		(containsStr(msg, "Heavenly Executioner") && containsStr(msg, "Stick")) ||
		(containsStr(msg, "Heavenly Executioner") && containsStr(msg, "Dagger")) ||
		(containsStr(msg, "Heavenly Executioner") && containsStr(msg, "Scaled")) ||
		(containsStr(msg, "Heavenly Executioner") && containsStr(msg, "Armor")) ||
		(containsStr(msg, "Heavenly Executioner") && containsStr(msg, "of Thief")) ||
		(containsStr(msg, "Heavenly Executioner") && containsStr(msg, "DaeChangEau"))
		) {
		return true;
	}
	return false;
}

bool checkG80AW(std::string msg) {

	if ((containsStr(msg, "Infernal Destroyer") && containsStr(msg, "Sword")) ||
		(containsStr(msg, "Infernal Destroyer") && containsStr(msg, "Bow")) ||
		(containsStr(msg, "Infernal Destroyer") && containsStr(msg, "Stick")) ||
		(containsStr(msg, "Infernal Destroyer") && containsStr(msg, "Dagger")) ||
		(containsStr(msg, "Infernal Destroyer") && containsStr(msg, "Scaled")) ||
		(containsStr(msg, "Infernal Destroyer") && containsStr(msg, "Armor")) ||
		(containsStr(msg, "Infernal Destroyer") && containsStr(msg, "of Thief")) ||
		(containsStr(msg, "Infernal Destroyer") && containsStr(msg, "DaeChangEau"))
		) {
		return true;
	}
	return false;
}

bool checkG90AW(std::string msg) {
	if ((containsStr(msg, "Dark Shadow") && containsStr(msg, "Sword")) ||
		(containsStr(msg, "Dark Shadow") && containsStr(msg, "Bow")) ||
		(containsStr(msg, "Dark Shadow") && containsStr(msg, "Stick")) ||
		(containsStr(msg, "Dark Shadow") && containsStr(msg, "Dagger")) ||
		(containsStr(msg, "Dark Shadow") && containsStr(msg, "Scaled")) ||
		(containsStr(msg, "Dark Shadow") && containsStr(msg, "Armor")) ||
		(containsStr(msg, "Dark Shadow") && containsStr(msg, "of Thief")) ||
		(containsStr(msg, "Dark Shadow") && containsStr(msg, "DaeChangEau"))
		) {
		return true;
	}
	return false;
}