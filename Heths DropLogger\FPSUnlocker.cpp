#include "StdAfx.h"
#include "FPSUnlocker.h"

#include "Interface.h"

#include "Engine.h"
#include "Tools.h"
#include "Graphics.h"

CREATE_INTERFACE(FPSUnlocker)

FPSUnlocker::FPSUnlocker()
{
}


// Static wrapper function for the hook - using naked function to preserve calling convention
__declspec(naked) int RenderWrapper()
{
	__asm
	{
		push ecx  // Save the 'this' pointer
		call FPSUnlocker::Render3DEnvironment
		ret
	}
}

void FPSUnlocker::Hook()
{
	Interface<ITools> Tools;
	Tools->Intercept(ITools::_I_CALL, (void*)0x00482D90, (void*)RenderWrapper, 5);
	//Tools->Intercept(ITools::_I_CALL, (void*)0x00674f0c, (void*)RenderWrapper, 5);
}
// functions
//Engine::CD3DApplication::Render3DEnvironment
//Engine::CD3DApplication::Reset3DEnvironment
//Engine::KWindowCollector::ProcessDestroy
//Engine::CD3DApplication::DXUtil_Timer
//Engine::CD3DApplication::ChkDataMode


// New Render3DEnvironment function based on IDA disassembly
int FPSUnlocker::Render3DEnvironment(void* _this)
{
	DWORD* thisPtr = (DWORD*)_this;

	Engine::KGameSys::ChkDataMode(4, "Render3DEnvironment Start\n");

	// Check if device is lost (offset 158 = 0x9E)
	if (*(BYTE*)(thisPtr + 158))
	{
		Engine::KGameSys::ChkDataMode(4, "Render3DEnvironment m_bDeviceLost\n");

		// Get D3D device pointer (offset 240 = 0xF0) and call TestCooperativeLevel
		DWORD* d3dDevice = *(DWORD**)(thisPtr + 240);
		int result = ((int(__stdcall*)(DWORD*))(*d3dDevice + 12))(d3dDevice);

		if (result < 0)
		{
			if (result == (int)0x88760868) // D3DERR_DEVICELOST
			{
				Engine::KGameSys::ChkDataMode(4, "Render3DEnvironment D3DERR_DEVICELOST\n");
				return 0;
			}
			else if (result == (int)0x88760869) // D3DERR_DEVICENOTRESET
			{
				Engine::KGameSys::ChkDataMode(4, "Render3DEnvironment D3DERR_DEVICENOTRESET\n");

				// Check if we can reset (offset 156 = 0x9C)
				if (*(BYTE*)(thisPtr + 156))
				{
					DWORD* v2;
					// Check windowed mode (offset 48 = 0x30)
					if (*(BYTE*)(thisPtr + 48))
						v2 = *(DWORD**)(thisPtr + 52); // offset 52 = 0x34
					else
						v2 = *(DWORD**)(thisPtr + 108); // offset 108 = 0x6C

					// Call some D3D function (offset 236 = 0xEC)
					DWORD* d3dObject = *(DWORD**)(thisPtr + 236);
					((void(__stdcall*)(DWORD*, DWORD, DWORD*))((*d3dObject) + 32))(d3dObject, *v2, (DWORD*)(thisPtr + 64));

					// Update some value (offset 176 = 0xB0, offset 76 = 0x4C)
					*(DWORD*)(thisPtr + 176) = *(DWORD*)(thisPtr + 76);
				}

				// Call Reset3DEnvironment
				result = Engine::CD3DApplication::Reset3DEnvironment(_this);
				if (result < 0)
				{
					Engine::KGameSys::ChkDataMode(4, "Render3DEnvironment Reset3DEnvironment Error\n");
					return result;
				}
			}
			else
			{
				Engine::KGameSys::ChkDataMode(4, "Render3DEnvironment TestCooperativeLevel Error\n");
				return result;
			}
		}

		// Clear device lost flag
		*(BYTE*)(thisPtr + 158) = 0;
	}

	// Get timing values
	float v7 = Engine::CD3DApplication::DXUtil_Timer(5);
	float v9 = Engine::CD3DApplication::DXUtil_Timer(6);

	// Check for time skip (offset 164 = 0xA4)
	if (v9 == 0.0f && *(BYTE*)(thisPtr + 164))
	{
		Engine::KGameSys::ChkDataMode(4, "Render3DEnvironment Time Skip\n");
		return 0;
	}

	// Check if we need to update frame (offset 164 = 0xA4, offset 165 = 0xA5)
	if (*(BYTE*)(thisPtr + 164) || *(BYTE*)(thisPtr + 165))
	{
		// Store timing values (offset 620 = 0x26C, offset 624 = 0x270)
		*(float*)(thisPtr + 620) = v7;
		*(float*)(thisPtr + 624) = v9;

		// Call FrameMove (virtual function at offset 24 in vtable)
		int frameResult = ((int(__thiscall*)(void*))(*(DWORD*)thisPtr + 24))(_this);
		if (frameResult < 0)
		{
			Engine::KGameSys::ChkDataMode(4, "Render3DEnvironment FrameMove Fail\n");
			return frameResult;
		}

		// Clear flag (offset 165 = 0xA5)
		*(BYTE*)(thisPtr + 165) = 0;
	}

	// Call Render (virtual function at offset 28 in vtable)
	int renderResult = ((int(__thiscall*)(void*))(*(DWORD*)thisPtr + 28))(_this);
	if (renderResult >= 0)
	{
		// Call some cleanup function (virtual function at offset 4 in vtable)
		((void(__thiscall*)(void*))(*(DWORD*)thisPtr + 4))(_this);

		// Present the scene (offset 240 = 0xF0)
		DWORD* d3dDevice = *(DWORD**)(thisPtr + 240);
		int presentResult = ((int(__stdcall*)(DWORD*, DWORD, DWORD, DWORD, DWORD))((*d3dDevice) + 68))(d3dDevice, 0, 0, 0, 0);
		if (presentResult == (int)0x88760868) // D3DERR_DEVICELOST
		{
			*(BYTE*)(thisPtr + 158) = 1; // Set device lost flag
		}

		Engine::KGameSys::ChkDataMode(4, "Render3DEnvironment End\n");

		// FPS limiting logic - MODIFIED FOR 144 FPS CAP
		int v8 = (int)(*(float*)(thisPtr + 624) * 1000.0f);
		const int TARGET_FRAME_TIME_144FPS = 7; // ~6.94ms for 144 FPS, rounded to 7ms

		if (v8 <= 0 || v8 >= TARGET_FRAME_TIME_144FPS) // Cap at ~144 FPS (7ms)
		{
			// No sleep needed if frame time is already >= target
			*(DWORD*)(thisPtr + 856) = 0;
			*(BYTE*)(thisPtr + 860) = 0;
		}
		else
		{
			// Calculate sleep time to maintain 144 FPS cap
			*(DWORD*)(thisPtr + 856) = TARGET_FRAME_TIME_144FPS - v8;

			// Additional timing adjustment (adapted from original code)
			if (*(float*)(thisPtr + 628) > 58.0f)
			{
				*(DWORD*)(thisPtr + 856) += ((int)*(float*)(thisPtr + 628) - 58) / 3;
				if (*(int*)(thisPtr + 856) > TARGET_FRAME_TIME_144FPS)
					*(DWORD*)(thisPtr + 856) = TARGET_FRAME_TIME_144FPS;
			}

			if (*(int*)(thisPtr + 856) <= 0)
			{
				*(BYTE*)(thisPtr + 860) = 0;
			}
			else
			{
				*(BYTE*)(thisPtr + 860) = 1;
				Sleep(*(DWORD*)(thisPtr + 856) + 1);
			}
		}

		return 0;
	}
	else
	{
		Engine::KGameSys::ChkDataMode(4, "Render3DEnvironment Render Fail\n");
		return renderResult;
	}
}

// Main render function that gets hooked
int __thiscall FPSUnlocker::Render(void* _this)
{
	// Simply call our custom Render3DEnvironment function
	return FPSUnlocker::Render3DEnvironment(_this);
}