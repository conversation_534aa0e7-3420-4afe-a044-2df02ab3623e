#include "StdAfx.h"
#include "FPSUnlocker.h"

#include "Interface.h"

#include "Engine.h"
#include "Tools.h"
#include "Graphics.h"

CREATE_INTERFACE(FPSUnlocker)

FPSUnlocker::FPSUnlocker()
{
}


void FPSUnlocker::Hook()
{
	Interface<ITools> Tools;
	Tools->Intercept(ITools::_I_CALL, (void*)0x00482D90, FPSUnlocker::Render, 5);
	//Tools->Intercept(ITools::_I_CALL, (void*)0x00674f0c, FPSUnlocker::Render, 5);
}
// functions
//Engine::CD3DApplication::Render3DEnvironment
//Engine::CD3DApplication::Reset3DEnvironment
//Engine::KWindowCollector::ProcessDestroy
//Engine::CD3DApplication::DXUtil_Timer
//Engine::CD3DApplication::ChkDataMode

int __thiscall FPSUnlocker::Render(void* _this)
{
	// DWORD *that = thisPtr;
	// Ignored by compiler, needs to be forced
	DWORD* that;
	__asm mov that, ecx

	Interface<IGraphics> Graphics;
	Interface<FPSUnlocker> FPSUnlocker;

	// Load keyboard accelerators
	HACCEL hAccel = LoadAcceleratorsA(NULL, MAKEINTRESOURCEA(113));

	void(__thiscall * Unknown1)(void* _this) = (void(__thiscall*)(void*))0x004564e0;
	void(__thiscall * Unknown2)(void* _this) = (void(__thiscall*)(void*))0x005c74e0;
	void(__thiscall * Unknown3)(void* _this) = (void(__thiscall*)(void*))0x005797e0;

	bool* m_bActive = (bool*)(*that + 0x9d);
	HWND* m_hWnd = (HWND*)(*that + 0x38);
	bool* Pub_Activate = (bool*)0x007616a8;
	int* UnknownInstance = (int*)0x008901b4;

	int PerformanceTime;
	unsigned __int64 lpFrequency;
	unsigned __int64 lpPerformanceCount;
	unsigned __int64 lpPerformanceStep;

	bool bGotMsg;
	MSG msg;
	msg.message = WM_NULL;
	PeekMessageA(&msg, NULL, 0U, 0U, PM_NOREMOVE);

	QueryPerformanceFrequency((LARGE_INTEGER*)&lpFrequency);

	while (msg.message != WM_QUIT)
	{
		Unknown1(that);
		Unknown2(that);

		if (*UnknownInstance) Unknown3((void*)*UnknownInstance);

		// Use PeekMessage() if the app is active, so we can use idle time to
		// render the scene. Else, use GetMessage() to avoid eating CPU time.
		if (*m_bActive) bGotMsg = (PeekMessageA(&msg, NULL, 0U, 0U, PM_REMOVE) != 0);
		else bGotMsg = (GetMessageA(&msg, NULL, 0U, 0U) != 0);

		if (bGotMsg)
		{
			// Translate and dispatch the message
			if (hAccel == NULL || *m_hWnd == NULL || !TranslateAcceleratorA(*m_hWnd, hAccel, &msg))
			{
				TranslateMessage(&msg);
				DispatchMessageA(&msg);
			}
			Engine::KWindowCollector::ProcessDestroy();
		}
		else
		{
			if (*m_bActive && *Pub_Activate)
			{
				Graphics->Lock();
				QueryPerformanceCounter((LARGE_INTEGER*)&lpPerformanceCount);
				if (Engine::CD3DApplication::Render3DEnvironment(that) < 0) SendMessageA(*m_hWnd, WM_CLOSE, 0, 0);
				QueryPerformanceCounter((LARGE_INTEGER*)&lpPerformanceStep);
				Graphics->Unlock();

				if (lpPerformanceStep - lpPerformanceCount) PerformanceTime = (int)(lpFrequency / (lpPerformanceStep - lpPerformanceCount));
				if (PerformanceTime) PerformanceTime = 1000 / PerformanceTime;
				if (FPSUnlocker->_MaxTime > PerformanceTime)
				{
					unsigned long SleepTime = (FPSUnlocker->_MaxTime - PerformanceTime);
					Sleep(SleepTime);
				}
			}
		}
	}
	if (hAccel != NULL) DestroyAcceleratorTable(hAccel);

	return (INT)msg.wParam;
}