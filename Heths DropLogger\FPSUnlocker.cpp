#include "StdAfx.h"
#include "FPSUnlocker.h"

#include "Interface.h"

#include "Engine.h"
#include "Tools.h"
#include "Graphics.h"

CREATE_INTERFACE(FPSUnlocker)

FPSUnlocker::FPSUnlocker()
{
}


// Static wrapper function for the hook - using naked function to preserve calling convention
__declspec(naked) int RenderWrapper()
{
	__asm
	{
		push ecx  // Save the 'this' pointer
		call FPSUnlocker::Render3DEnvironment
		ret
	}
}

void FPSUnlocker::Hook()
{
	Interface<ITools> Tools;
	Tools->Intercept(ITools::_I_CALL, (void*)0x00482D90, (void*)RenderWrapper, 5);
	//Tools->Intercept(ITools::_I_CALL, (void*)0x00674f0c, (void*)RenderWrapper, 5);
}
// functions
//Engine::CD3DApplication::Render3DEnvironment
//Engine::CD3DApplication::Reset3DEnvironment
//Engine::KWindowCollector::ProcessDestroy
//Engine::CD3DApplication::DXUtil_Timer
//Engine::CD3DApplication::ChkDataMode

// Original message loop function - keeping for reference
//int __thiscall FPSUnlocker::OriginalRender(void* _this)
//{
//	// DWORD *that = thisPtr;
//	// Ignored by compiler, needs to be forced
//	DWORD* that;
//	__asm mov that, ecx
//
//	Interface<IGraphics> Graphics;
//	Interface<FPSUnlocker> FPSUnlocker;
//
//	// Load keyboard accelerators
//	HACCEL hAccel = LoadAcceleratorsA(NULL, MAKEINTRESOURCEA(113));
//
//	void(__thiscall * Unknown1)(void* _this) = (void(__thiscall*)(void*))0x004564e0;
//	void(__thiscall * Unknown2)(void* _this) = (void(__thiscall*)(void*))0x005c74e0;
//	void(__thiscall * Unknown3)(void* _this) = (void(__thiscall*)(void*))0x005797e0;
//
//	bool* m_bActive = (bool*)(*that + 0x9d);
//	HWND* m_hWnd = (HWND*)(*that + 0x38);
//	bool* Pub_Activate = (bool*)0x007616a8;
//	int* UnknownInstance = (int*)0x008901b4;
//
//	int PerformanceTime;
//	unsigned __int64 lpFrequency;
//	unsigned __int64 lpPerformanceCount;
//	unsigned __int64 lpPerformanceStep;
//
//	bool bGotMsg;
//	MSG msg;
//	msg.message = WM_NULL;
//	PeekMessageA(&msg, NULL, 0U, 0U, PM_NOREMOVE);
//
//	QueryPerformanceFrequency((LARGE_INTEGER*)&lpFrequency);
//
//	while (msg.message != WM_QUIT)
//	{
//		Unknown1(that);
//		Unknown2(that);
//
//		if (*UnknownInstance) Unknown3((void*)*UnknownInstance);
//
//		// Use PeekMessage() if the app is active, so we can use idle time to
//		// render the scene. Else, use GetMessage() to avoid eating CPU time.
//		if (*m_bActive) bGotMsg = (PeekMessageA(&msg, NULL, 0U, 0U, PM_REMOVE) != 0);
//		else bGotMsg = (GetMessageA(&msg, NULL, 0U, 0U) != 0);
//
//		if (bGotMsg)
//		{
//			// Translate and dispatch the message
//			if (hAccel == NULL || *m_hWnd == NULL || !TranslateAcceleratorA(*m_hWnd, hAccel, &msg))
//			{
//				TranslateMessage(&msg);
//				DispatchMessageA(&msg);
//			}
//			Engine::KWindowCollector::ProcessDestroy();
//		}
//		else
//		{
//			if (*m_bActive && *Pub_Activate)
//			{
//				Graphics->Lock();
//				QueryPerformanceCounter((LARGE_INTEGER*)&lpPerformanceCount);
//				if (FPSUnlocker::Render3DEnvironment(that) < 0) SendMessageA(*m_hWnd, WM_CLOSE, 0, 0);
//				QueryPerformanceCounter((LARGE_INTEGER*)&lpPerformanceStep);
//				Graphics->Unlock();
//
//				if (lpPerformanceStep - lpPerformanceCount) PerformanceTime = (int)(lpFrequency / (lpPerformanceStep - lpPerformanceCount));
//				if (PerformanceTime) PerformanceTime = 1000 / PerformanceTime;
//				if (FPSUnlocker->_MaxTime > PerformanceTime)
//				{
//					unsigned long SleepTime = (FPSUnlocker->_MaxTime - PerformanceTime);
//					Sleep(SleepTime);
//				}
//			}
//		}
//	}
//	if (hAccel != NULL) DestroyAcceleratorTable(hAccel);
//
//	return (INT)msg.wParam;
//}

// New Render3DEnvironment function based on IDA disassembly
int __thiscall FPSUnlocker::Render3DEnvironment(void* _this)
{
	DWORD* thisPtr = (DWORD*)_this;

	Engine::KGameSys::ChkDataMode(4, "Render3DEnvironment Start\n");

	// Check if device is lost (offset 158 = 0x9E)
	if (*(BYTE*)(thisPtr + 158))
	{
		Engine::KGameSys::ChkDataMode(4, "Render3DEnvironment m_bDeviceLost\n");

		// Get D3D device pointer (offset 240 = 0xF0) and call TestCooperativeLevel
		DWORD* d3dDevice = *(DWORD**)(thisPtr + 240);
		int result = ((int(__stdcall*)(DWORD*))(*d3dDevice + 12))(d3dDevice);

		if (result < 0)
		{
			if (result == (int)0x88760868) // D3DERR_DEVICELOST
			{
				Engine::KGameSys::ChkDataMode(4, "Render3DEnvironment D3DERR_DEVICELOST\n");
				return 0;
			}
			else if (result == (int)0x88760869) // D3DERR_DEVICENOTRESET
			{
				Engine::KGameSys::ChkDataMode(4, "Render3DEnvironment D3DERR_DEVICENOTRESET\n");

				// Check if we can reset (offset 156 = 0x9C)
				if (*(BYTE*)(thisPtr + 156))
				{
					DWORD* v2;
					// Check windowed mode (offset 48 = 0x30)
					if (*(BYTE*)(thisPtr + 48))
						v2 = *(DWORD**)(thisPtr + 52); // offset 52 = 0x34
					else
						v2 = *(DWORD**)(thisPtr + 108); // offset 108 = 0x6C

					// Call some D3D function (offset 236 = 0xEC)
					DWORD* d3dObject = *(DWORD**)(thisPtr + 236);
					((void(__stdcall*)(DWORD*, DWORD, DWORD*))((*d3dObject) + 32))(d3dObject, *v2, (DWORD*)(thisPtr + 64));

					// Update some value (offset 176 = 0xB0, offset 76 = 0x4C)
					*(DWORD*)(thisPtr + 176) = *(DWORD*)(thisPtr + 76);
				}

				// Call Reset3DEnvironment
				result = Engine::CD3DApplication::Reset3DEnvironment(_this);
				if (result < 0)
				{
					Engine::KGameSys::ChkDataMode(4, "Render3DEnvironment Reset3DEnvironment Error\n");
					return result;
				}
			}
			else
			{
				Engine::KGameSys::ChkDataMode(4, "Render3DEnvironment TestCooperativeLevel Error\n");
				return result;
			}
		}

		// Clear device lost flag
		*(BYTE*)(thisPtr + 158) = 0;
	}

	// Get timing values
	float v7 = Engine::CD3DApplication::DXUtil_Timer(5);
	float v9 = Engine::CD3DApplication::DXUtil_Timer(6);

	// Check for time skip (offset 164 = 0xA4)
	if (v9 == 0.0f && *(BYTE*)(thisPtr + 164))
	{
		Engine::KGameSys::ChkDataMode(4, "Render3DEnvironment Time Skip\n");
		return 0;
	}

	// Check if we need to update frame (offset 164 = 0xA4, offset 165 = 0xA5)
	if (*(BYTE*)(thisPtr + 164) || *(BYTE*)(thisPtr + 165))
	{
		// Store timing values (offset 620 = 0x26C, offset 624 = 0x270)
		*(float*)(thisPtr + 620) = v7;
		*(float*)(thisPtr + 624) = v9;

		// Call FrameMove (virtual function at offset 24 in vtable)
		int frameResult = ((int(__thiscall*)(void*))(*(DWORD*)thisPtr + 24))(_this);
		if (frameResult < 0)
		{
			Engine::KGameSys::ChkDataMode(4, "Render3DEnvironment FrameMove Fail\n");
			return frameResult;
		}

		// Clear flag (offset 165 = 0xA5)
		*(BYTE*)(thisPtr + 165) = 0;
	}

	// Call Render (virtual function at offset 28 in vtable)
	int renderResult = ((int(__thiscall*)(void*))(*(DWORD*)thisPtr + 28))(_this);
	if (renderResult >= 0)
	{
		// Call some cleanup function (virtual function at offset 4 in vtable)
		((void(__thiscall*)(void*))(*(DWORD*)thisPtr + 4))(_this);

		// Present the scene (offset 240 = 0xF0)
		DWORD* d3dDevice = *(DWORD**)(thisPtr + 240);
		int presentResult = ((int(__stdcall*)(DWORD*, DWORD, DWORD, DWORD, DWORD))((*d3dDevice) + 68))(d3dDevice, 0, 0, 0, 0);
		if (presentResult == (int)0x88760868) // D3DERR_DEVICELOST
		{
			*(BYTE*)(thisPtr + 158) = 1; // Set device lost flag
		}

		Engine::KGameSys::ChkDataMode(4, "Render3DEnvironment End\n");

		// FPS limiting logic - THIS IS WHERE WE MODIFY FOR FPS UNLOCKING
		int v8 = (int)(*(float*)(thisPtr + 624) * 1000.0f);
		if (v8 <= 0 || v8 >= 17) // Original game limits to ~60 FPS (17ms)
		{
			// Remove FPS cap - don't sleep
			*(DWORD*)(thisPtr + 856) = 0;
			*(BYTE*)(thisPtr + 860) = 0;
		}
		else
		{
			// MODIFIED: Remove the FPS limiting sleep
			// Original code would calculate sleep time and call Sleep()
			// We set sleep time to 0 to unlock FPS
			*(DWORD*)(thisPtr + 856) = 0; // No sleep time
			*(BYTE*)(thisPtr + 860) = 0;  // No sleep flag

			// Original FPS limiting code (commented out):
			/*
			*(DWORD*)(thisPtr + 856) = 17 - v8;
			if (*(float*)(thisPtr + 628) > 58.0f)
			{
				*(DWORD*)(thisPtr + 856) += ((int)*(float*)(thisPtr + 628) - 58) / 3;
				if (*(int*)(thisPtr + 856) > 17)
					*(DWORD*)(thisPtr + 856) = 17;
			}
			if (*(int*)(thisPtr + 856) <= 0)
			{
				*(BYTE*)(thisPtr + 860) = 0;
			}
			else
			{
				*(BYTE*)(thisPtr + 860) = 1;
				Sleep(*(DWORD*)(thisPtr + 856) + 1);
			}
			*/
		}

		return 0;
	}
	else
	{
		Engine::KGameSys::ChkDataMode(4, "Render3DEnvironment Render Fail\n");
		return renderResult;
	}
}

// Main render function that gets hooked
int __thiscall FPSUnlocker::Render(void* _this)
{
	// Simply call our custom Render3DEnvironment function
	return FPSUnlocker::Render3DEnvironment(_this);
}