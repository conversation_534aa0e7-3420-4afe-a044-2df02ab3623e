﻿C:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Microsoft\VC\v160\Microsoft.CppBuild.targets(513,5): warning MSB8028: The intermediate directory (Release\) contains files shared from another project (Heths DropLogger.vcxproj).  This can lead to incorrect clean and rebuild behavior.
  dllmain.cpp
  FPSUnlocker.cpp
  Graphics.cpp
  Interface.cpp
  Tools.cpp
G:\heth\my apps\Sources 2025\Heth FPSUnlocker\Heths DropLogger\Tools.cpp(329,32): warning C4302: 'type cast': truncation from 'void *' to 'BYTE'
G:\heth\my apps\Sources 2025\Heth FPSUnlocker\Heths DropLogger\Tools.cpp(333,32): warning C4302: 'type cast': truncation from 'void *' to 'BYTE'
  Generating code
  Previous IPDB and IOBJ mismatch, fall back to full compilation.
  All 311 functions were compiled because no usable IPDB/IOBJ from previous compilation was found.
  Finished generating code
  Heths DropLogger.vcxproj -> G:\heth\my apps\Sources 2025\Heth FPSUnlocker\Release\Heth FPSUnlocker.dll
