﻿C:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Microsoft\VC\v160\Microsoft.CppBuild.targets(513,5): warning MSB8028: The intermediate directory (Release\) contains files shared from another project (Heths DropLogger.vcxproj).  This can lead to incorrect clean and rebuild behavior.
  FPSUnlocker.cpp
G:\heth\my apps\Sources 2025\Heth FPSUnlocker\Heths DropLogger\FPSUnlocker.cpp(20,74): error C3867: 'FPSUnlocker::Render': non-standard syntax; use '&' to create a pointer to member
  dllmain.cpp
