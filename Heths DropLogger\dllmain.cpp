#pragma comment(lib,"Detours/detours.lib")

#include <Windows.h>
#include "Detours/detours.h"
#include <iostream>
#include <sstream>  
#include <fstream>
#include "Engine.h"
#include "Interface.h"
#include "FPSUnlocker.h"

static int(__cdecl* AddInfoMessage)(const char* Message, int Color, int type) = (int(__cdecl*)(const char*, int, int))0x006CC8C0; // 2019
static void(__cdecl* AddChattingMessage)(char Type, const char* Message, int Color) = (void(__cdecl*)(char, const char*, int))0x006CC890; // 2019


void HookUnlocker(){
	Interface<FPSUnlocker> Unlocker;
	Unlocker->Hook();
}


DWORD WINAPI MainThread(HMODULE hModule)
{


	while (true)
	{
		if (GetAsyncKeyState(VK_F2) & 1)
		{
			AddChattingMessage(1, "[Heth FPSUnlock]: Hello im here.", 0x0000ff);
			break;
		}

		Sleep(10);
	}


	FreeLibraryAndExitThread(hModule, 0);
	return 0;
}







BOOL APIENTRY DllMain(HMODULE hModule, DWORD ul_reason_for_call, LPVOID lpReserved) {
	switch (ul_reason_for_call)
	{
	case DLL_PROCESS_ATTACH:
	{

		CloseHandle(CreateThread(nullptr, 0, (LPTHREAD_START_ROUTINE)MainThread, hModule, 0, nullptr));
		DetourTransactionBegin();
		//DetourAttach(&(PVOID&)AddInfoMessage, MyAddInfoMessage);
		//DetourAttach(&(PVOID&)_PWDPK, PWDPK);
		DetourTransactionCommit();
		break;
	}
	case DLL_PROCESS_DETACH:
	{
		DetourTransactionBegin();
		//DetourDetach(&(PVOID&)_OpenPK, OpenPK);
		DetourTransactionCommit();
		//FreeConsole();
		//FreeLibraryAndExitThread(hModule, 0);
	}
	break;
	}
	return TRUE;
}